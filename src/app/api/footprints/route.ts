import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// 足迹点类型定义
interface FootprintPoint {
  latitude: number;
  longitude: number;
  timestamp: string; // ISO 8601 格式
}

// 计算两点之间的距离（使用 Haversine 公式，返回公里）
function calculateDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number,
): number {
  const R = 6371; // 地球半径（公里）
  const dLat = ((lat2 - lat1) * Math.PI) / 180;
  const dLon = ((lon2 - lon1) * Math.PI) / 180;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((lat1 * Math.PI) / 180) *
      Math.cos((lat2 * Math.PI) / 180) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

// 计算足迹记录的总距离
function calculateTotalDistance(footPrints: FootprintPoint[]): number {
  if (footPrints.length < 2) return 0;

  let totalDistance = 0;
  for (let i = 1; i < footPrints.length; i++) {
    const prev = footPrints[i - 1];
    const curr = footPrints[i];
    totalDistance += calculateDistance(
      prev.latitude,
      prev.longitude,
      curr.latitude,
      curr.longitude,
    );
  }
  return Math.round(totalDistance * 100) / 100; // 保留两位小数
}

// 创建新的足迹记录
export async function POST(req: NextRequest) {
  try {
    const {
      userId,
      footPrints,
      activityType,
      isFinished = false,
    } = await req.json();

    // 参数验证
    if (!userId) {
      return NextResponse.json({ error: "缺少userId参数" }, { status: 400 });
    }

    if (!activityType) {
      return NextResponse.json(
        { error: "缺少activityType参数" },
        { status: 400 },
      );
    }

    // 验证活动类型
    const validActivityTypes = ["walking", "cycling", "bus", "subway"];
    if (!validActivityTypes.includes(activityType)) {
      return NextResponse.json(
        { error: "无效的activityType，必须是: walking, cycling, bus, subway" },
        { status: 400 },
      );
    }

    // 验证用户存在
    const user = await prisma.user.findUnique({
      where: { userId },
    });

    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 404 });
    }

    // 处理足迹点数据，如果没有提供则初始化为空数组
    let processedFootPrints: FootprintPoint[] = [];
    if (footPrints && Array.isArray(footPrints)) {
      // 验证足迹点格式并添加时间戳
      processedFootPrints = footPrints.map((point: any) => {
        if (
          typeof point.latitude !== "number" ||
          typeof point.longitude !== "number"
        ) {
          throw new Error("足迹点必须包含有效的latitude和longitude");
        }
        return {
          latitude: point.latitude,
          longitude: point.longitude,
          timestamp: new Date().toISOString(), // 后端生成时间戳
        };
      });
    }

    // 计算总距离
    const totalDistance = calculateTotalDistance(processedFootPrints);

    // 创建足迹记录
    const footprint = await prisma.userFootprints.create({
      data: {
        userId,
        footPrints: processedFootPrints as any, // 类型转换为 JSON
        activityType,
        isFinished,
        totalDistance,
      },
    });

    return NextResponse.json({
      success: true,
      message: "足迹记录创建成功",
      data: footprint,
    });
  } catch (error) {
    console.error("创建足迹记录失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}

// 查询用户的足迹记录（GET请求习惯直接提取参数）
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");
    const activityType = searchParams.get("activityType");

    if (!userId) {
      return NextResponse.json({ error: "缺少userId参数" }, { status: 400 });
    }

    // 验证用户存在
    const user = await prisma.user.findUnique({
      where: { userId },
    });

    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 404 });
    }

    // 构建查询条件
    const whereConditions: any = {
      userId,
    };

    // 添加时间范围过滤
    if (startDate || endDate) {
      whereConditions.createdAt = {};
      if (startDate) {
        whereConditions.createdAt.gte = new Date(startDate);
      }
      if (endDate) {
        whereConditions.createdAt.lte = new Date(endDate);
      }
    }

    // 添加活动类型过滤
    if (activityType) {
      const validActivityTypes = ["walking", "cycling", "bus", "subway"];
      if (!validActivityTypes.includes(activityType)) {
        return NextResponse.json(
          { error: "无效的activityType" },
          { status: 400 },
        );
      }
      whereConditions.activityType = activityType;
    }

    // 查询足迹记录
    const footprints = await prisma.userFootprints.findMany({
      where: whereConditions,
      orderBy: {
        createdAt: "desc", // 按创建时间倒序
      },
    });

    return NextResponse.json({
      success: true,
      data: footprints,
    });
  } catch (error) {
    console.error("查询足迹记录失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}

// 更新足迹记录（追加足迹点或更新状态）
export async function PATCH(req: NextRequest) {
  try {
    const { footprintId, footPrints, activityType, isFinished } =
      await req.json();

    if (!footprintId) {
      return NextResponse.json({ error: "缺少足迹记录ID" }, { status: 400 });
    }

    // 验证足迹记录存在
    const existingFootprint = await prisma.userFootprints.findUnique({
      where: { id: footprintId },
    });

    if (!existingFootprint) {
      return NextResponse.json({ error: "足迹记录不存在" }, { status: 404 });
    }

    // 准备更新数据
    const updateData: any = {};

    // 如果提供了新的足迹点，追加到现有数组
    if (footPrints && Array.isArray(footPrints)) {
      const existingPoints =
        existingFootprint.footPrints as unknown as FootprintPoint[];
      const newPoints = footPrints.map((point: any) => {
        if (
          typeof point.latitude !== "number" ||
          typeof point.longitude !== "number"
        ) {
          throw new Error("足迹点必须包含有效的latitude和longitude");
        }
        return {
          latitude: point.latitude,
          longitude: point.longitude,
          timestamp: new Date().toISOString(), // 后端生成时间戳
        };
      });
      const updatedPoints = [...existingPoints, ...newPoints];
      updateData.footPrints = updatedPoints as any; // 类型转换为 JSON

      // 重新计算总距离
      updateData.totalDistance = calculateTotalDistance(updatedPoints);
    }

    // 更新活动类型（如果提供）
    if (activityType) {
      const validActivityTypes = ["walking", "cycling", "bus", "subway"];
      if (!validActivityTypes.includes(activityType)) {
        return NextResponse.json(
          { error: "无效的activityType" },
          { status: 400 },
        );
      }
      updateData.activityType = activityType;
    }

    // 更新完成状态（如果提供）
    if (typeof isFinished === "boolean") {
      updateData.isFinished = isFinished;
    }

    // 执行更新
    const updatedFootprint = await prisma.userFootprints.update({
      where: { id: footprintId },
      data: updateData,
    });

    return NextResponse.json({
      success: true,
      message: "足迹记录更新成功",
      data: updatedFootprint,
    });
  } catch (error) {
    console.error("更新足迹记录失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}

// 删除足迹记录
export async function DELETE(req: NextRequest) {
  try {
    const { footprintId } = await req.json();

    if (!footprintId) {
      return NextResponse.json({ error: "缺少足迹记录ID" }, { status: 400 });
    }

    // 验证足迹记录存在
    const existingFootprint = await prisma.userFootprints.findUnique({
      where: { id: footprintId },
    });

    if (!existingFootprint) {
      return NextResponse.json({ error: "足迹记录不存在" }, { status: 404 });
    }

    // 删除足迹记录
    await prisma.userFootprints.delete({
      where: { id: footprintId },
    });

    return NextResponse.json({
      success: true,
      message: "足迹记录删除成功",
    });
  } catch (error) {
    console.error("删除足迹记录失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}
